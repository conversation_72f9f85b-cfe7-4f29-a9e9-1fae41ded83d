#include <ctime>
#include "elecNodeAcuvim.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 10
elecNodeAcuvim::elecNodeAcuvim()
{
  kwh = 0;
  kw = 0;
  volage = 0;
  current = 0;
  freq = 0;
  power_factor = 0;
  update_kwh_timestamp = 0;
  update_timestamp = 0;
  old_kwh = 0;
}

elecNodeAcuvim::~elecNodeAcuvim()
{
}

int elecNodeAcuvim::set(Json::Value it)
{
  // cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  analogNode::set(it);
  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");

  int reg_arr[]=
  {
    16384, // Frequency
    16394, // V
    16402, // I
    16418, //kw
    16426, //kvar
    16442, // pf
    16464
  };
  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;

  for (int i = 0; i < sizeof(reg_arr) / sizeof(reg_arr[0]); i++)
  {
    send[0] = m_addr;
    send[1] = 0x03;
    send[2] = (reg_arr[i] +0)/ 256;
    send[3] = (reg_arr[i] +0)% 256;
    send[4] = 0;
    send[5] = 0x02;
    if (i == 1)
    {
      send[5] = 0x06;
    }
    else if (i == 2)
    {
      send[5] = 0x08;
    }


    crc = crc_chk(send, 6);
    send[6] = crc % 256;
    send[7] = crc / 256;

    RS485Msg msg;
    msg.setData(send, 8);
    m_msg.push_back(msg);
  }

  iterator = m_msg.begin();

  baUtil::addElecNodes(this);

  return 1;
}

bool elecNodeAcuvim::set_data(uint8_t *p_data, int len)
{
  if (p_data[0] != m_addr)
  {
    return false;
  }
  
  int reg_arr[]=
  {
    16384, // Frequency 0x4000
    16394, // V 0x400a
    16402, // I 0x4012
    16418, //kw 0x4022
    16426, //kvar 0x402a
    16442, // pf 0x403a
    16464 //kwh 0x4050
  };
  int aspect_length = 7;
  if (m_index == 1)
  {
    aspect_length = 15;
  }
  else if (m_index == 2)
  {
    aspect_length = 19;
  }
  uint16_t crc = crc_chk(p_data, aspect_length);
  if (p_data[1] == 0x83 || p_data[1] != 0x03)
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
      /* code */
    }

    cout << "elecNodeAcuvim response 83 error" << endl;
    return true;
  }
  if (crc % 256 != p_data[aspect_length] || crc / 256 != p_data[aspect_length + 1])
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
      /* code */
    }

    cout << "elecNodeAcuvim crc checksum error " << aspect_length << ", m_index:" << m_index << endl;
    return true;
  }
  //   int reg_arr[]=
  // {
  //   16384, // Frequency
  //   16394, // V
  //   16402, // I
  //   16418, //kw
  //   16426, //kvar
  //   16442, // pf
  // };
  unsigned int int_val;
  uint8_t buf4[4];

  int_val = *(int *)&buf4[0];
  switch (m_index)
  {
  case 0:  
    buf4[0] = p_data[6];
    buf4[1] = p_data[5];
    buf4[2] = p_data[4];
    buf4[3] = p_data[3];
    freq = (int)(*((float *)&buf4[0])*100);
    break;
  case 1:
    buf4[0] = p_data[6];
    buf4[1] = p_data[5];
    buf4[2] = p_data[4];
    buf4[3] = p_data[3];    
    volage = (int)(*((float *)&buf4[0])*100); 
    buf4[0] = p_data[10];
    buf4[1] = p_data[9];
    buf4[2] = p_data[8];
    buf4[3] = p_data[7];
    V_BN = (int)(*((float *)&buf4[0])*100);
    buf4[0] = p_data[14];
    buf4[1] = p_data[13];
    buf4[2] = p_data[12];
    buf4[3] = p_data[11];
    V_CN = (int)(*((float *)&buf4[0])*100);
    break;
  case 2:
    buf4[0] = p_data[6];
    buf4[1] = p_data[5];
    buf4[2] = p_data[4];
    buf4[3] = p_data[3];
    A_A = (int)(*((float *)&buf4[0])*100);
    buf4[0] = p_data[10];
    buf4[1] = p_data[9];
    buf4[2] = p_data[8];
    buf4[3] = p_data[7];
    A_B = (int)(*((float *)&buf4[0])*100);
    buf4[0] = p_data[14];
    buf4[1] = p_data[13];
    buf4[2] = p_data[12];
    buf4[3] = p_data[11];
    A_C = (int)(*((float *)&buf4[0])*100);

    buf4[0] = p_data[18];
    buf4[1] = p_data[17];
    buf4[2] = p_data[16];
    buf4[3] = p_data[15];
    current = (int)(*((float *)&buf4[0])*100);
    break;
  case 3:
    buf4[0] = p_data[6];
    buf4[1] = p_data[5];
    buf4[2] = p_data[4];
    buf4[3] = p_data[3];
    kw = (int)(*((float *)&buf4[0])/1000*100);
    updateMessage(); // 新增：在 kw 更新後立即呼叫 updateMessage() 以觸發需量監控
    break;
  case 4:
    buf4[0] = p_data[6];
    buf4[1] = p_data[5];
    buf4[2] = p_data[4];
    buf4[3] = p_data[3];
    kvar = (int)(*((float *)&buf4[0])/1000*100);

    break;
  case 5:
    buf4[0] = p_data[6];
    buf4[1] = p_data[5];
    buf4[2] = p_data[4];
    buf4[3] = p_data[3];
    power_factor = (int)(*((float *)&buf4[0])*100);
    break;
  case 6:
    buf4[0] = p_data[6];
    buf4[1] = p_data[5];
    buf4[2] = p_data[4];
    buf4[3] = p_data[3];
    kwh = (long)(*((uint32_t *)&buf4[0])/10*100);
    // updateMessage() 已移至 case 3 (kw) 處理，確保功率值更新時立即觸發需量監控
    break;
  }

  m_index++;

  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }

  return true;
}